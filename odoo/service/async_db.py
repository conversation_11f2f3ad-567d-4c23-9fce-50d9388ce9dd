# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async database service operations for Odoo.
This module provides async versions of database management functions.
"""

import asyncio
import logging
import os
import shutil
import tempfile
from contextlib import asynccontextmanager
from datetime import datetime
from xml.etree import ElementTree as ET

import asyncpg
from decorator import decorator
from pytz import country_timezones

import odoo
import odoo.release
import odoo.sql_db
import odoo.tools
from odoo.async_sql_db import async_db_connect, connection_info_for
from odoo.tools import config
from odoo.tools.misc import file_path
from odoo.tools.sql import existing_tables
from odoo.http.utils import db_list

try:
    from odoo.async_error_handling import (
        AsyncDatabaseErrorHandler,
        AsyncRetryConfig,
        async_retry,
        async_error_context,
        AsyncDatabaseError
    )
except ImportError:
    # Fallback if error handling module is not available
    class AsyncDatabaseErrorHandler:
        @staticmethod
        async def with_retry(coro):
            return await coro
        @staticmethod
        async def with_timeout(coro, timeout=30.0):
            return await asyncio.wait_for(coro, timeout=timeout)

_logger = logging.getLogger(__name__)


def check_db_management_enabled(method):
    """Decorator to check if database management is enabled."""
    def wrapper(*args, **kwargs):
        if not config['list_db']:
            _logger.error('Database management functions blocked, admin disabled database listing')
            raise odoo.exceptions.AccessDenied()
        return method(*args, **kwargs)
    return wrapper


async def async_check_faketime_mode(db_name):
    """Check and configure faketime mode for database asynchronously."""
    if not config.get('test_enable'):
        return
    
    try:
        connection = await async_db_connect(db_name)
        async with connection.cursor() as cursor:
            # Set timezone and fake time for testing
            time_offset = config.get('test_time_offset', 0)
            await cursor.execute("""
                SET timezone = 'UTC';
                SELECT set_config('timezone', 'UTC', false);
                SELECT set_config('test.time_offset', %s, false);
            """, (int(time_offset),))
            
            await cursor.execute("SELECT (now() AT TIME ZONE 'UTC');")
            result = await cursor.fetchone()
            new_now = result[0] if result else None
            _logger.info("Faketime mode, new cursor now is %s", new_now)
            await cursor.commit()
    except Exception as e:
        _logger.warning("Unable to set fakedtimed NOW() : %s", e)


async def async_create_empty_database(name):
    """Create an empty database asynchronously."""
    connection = await async_db_connect('postgres')
    async with connection.cursor() as cr:
        # Database-altering operations cannot be executed inside a transaction
        await cr.execute("SET autocommit = true")
        
        # Check if database already exists
        await cr.execute("SELECT 1 FROM pg_database WHERE datname = %s", (name,))
        if await cr.fetchone():
            raise Exception(f"Database {name} already exists")
        
        # Create the database
        await cr.execute(f'CREATE DATABASE "{name}" ENCODING \'unicode\'')
        _logger.info('Created database %s', name)


async def async_create_database_extensions(name):
    """Create PostgreSQL extensions for the database asynchronously."""
    try:
        connection = await async_db_connect(name)
        async with connection.cursor() as cr:
            await cr.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
            if odoo.tools.config['unaccent']:
                await cr.execute("CREATE EXTENSION IF NOT EXISTS unaccent")
                await cr.execute("ALTER FUNCTION unaccent(text) IMMUTABLE")
    except Exception as e:  # Changed from psycopg2.Error
        _logger.warning("Unable to create PostgreSQL extensions : %s", e)
    
    await async_check_faketime_mode(name)

    # Restore legacy behaviour on pg15+
    try:
        connection = await async_db_connect(name)
        async with connection.cursor() as cr:
            await cr.execute("GRANT CREATE ON SCHEMA PUBLIC TO PUBLIC")
    except Exception as e:  # Changed from psycopg2.Error
        _logger.warning("Unable to make public schema public-accessible: %s", e)


@check_db_management_enabled
async def async_exp_create_database(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
    """Create a new database asynchronously."""
    _logger.info('Create database `%s`.', db_name)
    await async_create_empty_database(db_name)
    await async_initialize_db(db_name, demo, lang, user_password, login, country_code, phone)
    return True


async def async_initialize_db(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
    """Initialize a database with base modules asynchronously."""
    async with async_error_context(f"Database initialization for {db_name}"):
        # Step 1: Initialize database schema with base data
        connection = await AsyncDatabaseErrorHandler.with_timeout(
            async_db_connect(db_name), timeout=30.0
        )

        async with connection.cursor() as cr:
            await AsyncDatabaseErrorHandler.with_retry(
                async_db_initialize(cr)
            )
            import odoo.tools.config
            odoo.tools.config['load_language'] = lang
            await cr.commit()

        # Step 2: Create registry and load base modules
        # We need to use the synchronous registry creation for now
        # as the module loading system is not fully async yet
        import asyncio
        from functools import partial
        from odoo.modules.registry import Registry

        # Run registry creation in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        registry_func = partial(Registry.new, db_name, demo, None, update_module=True)
        registry = await AsyncDatabaseErrorHandler.with_timeout(
            loop.run_in_executor(None, registry_func), timeout=120.0
        )

        # Step 3: Configure the database with user settings
        await AsyncDatabaseErrorHandler.with_retry(
            async_configure_database(registry, lang, user_password, login, country_code, phone)
        )

        _logger.info('Database %s initialized successfully', db_name)


async def async_db_initialize(cr):
    """Async version of odoo.modules.db.initialize()."""
    import odoo.tools.misc
    import odoo.modules
    import json

    # Execute base SQL data
    try:
        f = odoo.tools.misc.file_path('base/data/base_data.sql')
    except FileNotFoundError:
        m = "File not found: 'base.sql' (provided by module 'base')."
        _logger.critical(m)
        raise IOError(m)

    with odoo.tools.misc.file_open(f) as base_sql_file:
        await cr.execute(base_sql_file.read())

    # Create module entries
    for i in odoo.modules.get_modules():
        mod_path = odoo.modules.get_module_path(i)
        if not mod_path:
            continue

        # This will raise an exception if no/unreadable descriptor file.
        info = odoo.modules.get_manifest(i)

        if not info:
            continue
        categories = info['category'].split('/')
        category_id = await async_create_categories(cr, categories)

        if info['installable']:
            state = 'uninstalled'
        else:
            state = 'uninstallable'

        await cr.execute('INSERT INTO ir_module_module \
                (author, website, name, shortdesc, description, \
                    category_id, auto_install, state, web, license, application, icon, sequence, summary) \
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) RETURNING id', (
            info['author'],
            info['website'], i, json.dumps({'en_US': info['name']}),
            json.dumps({'en_US': info['description']}), category_id,
            info['auto_install'] is not False, state,
            info['web'],
            info['license'],
            info['application'], info['icon'],
            info['sequence'], json.dumps({'en_US': info['summary']})))
        result = await cr.fetchone()
        id = result[0]
        await cr.execute('INSERT INTO ir_model_data \
            (name,model,module, res_id, noupdate) VALUES (%s,%s,%s,%s,%s)', (
                'module_'+i, 'ir.module.module', 'base', id, True))
        dependencies = info['depends']
        for d in dependencies:
            await cr.execute(
                'INSERT INTO ir_module_module_dependency (module_id, name, auto_install_required)'
                ' VALUES (%s, %s, %s)',
                (id, d, d in (info['auto_install'] or ()))
            )


async def async_create_categories(cr, categories):
    """Async version of create_categories from odoo.modules.db."""
    import json

    p_id = None
    while categories:
        if p_id is not None:
            await cr.execute('SELECT id FROM ir_module_category WHERE name=%s AND parent_id=%s', (categories[0], p_id))
        else:
            await cr.execute('SELECT id FROM ir_module_category WHERE name=%s AND parent_id is NULL', (categories[0],))
        c_id = await cr.fetchone()
        if not c_id:
            await cr.execute('INSERT INTO ir_module_category \
                    (name, parent_id) \
                    VALUES (%s, %s) RETURNING id', (categories[0], p_id))
            c_id = await cr.fetchone()
        c_id = c_id[0]
        p_id = c_id
        categories = categories[1:]
    return p_id


async def async_configure_database(registry, lang, user_password, login, country_code, phone):
    """Configure database with user settings asynchronously."""
    import asyncio
    from functools import partial
    import odoo.api
    from odoo import SUPERUSER_ID

    # Run database configuration in thread pool since ORM is not fully async
    loop = asyncio.get_event_loop()

    def configure_sync():
        with registry.cursor() as cr:
            env = odoo.api.Environment(cr, SUPERUSER_ID, {})

            if lang:
                modules = env['ir.module.module'].search([('state', '=', 'installed')])
                modules._update_translations(lang)

            if country_code:
                try:
                    country = env['res.country'].search([('code', 'ilike', country_code)])[0]
                    env['res.company'].browse(1).write({
                        'country_id': country_code and country.id,
                        'currency_id': country_code and country.currency_id.id
                    })
                    # Set timezone if unique for country
                    from odoo.addons.base.models.res_lang import country_timezones
                    if len(country_timezones.get(country_code, [])) == 1:
                        users = env['res.users'].search([])
                        users.write({'tz': country_timezones[country_code][0]})
                except Exception as e:
                    _logger.warning("Could not set country settings: %s", e)

            if phone:
                env['res.company'].browse(1).write({'phone': phone})
            if '@' in login:
                env['res.company'].browse(1).write({'email': login})

            # Update admin's password and lang and login
            values = {'password': user_password, 'lang': lang}
            if login:
                values['login'] = login
                import odoo.tools
                emails = odoo.tools.email_split(login)
                if emails:
                    values['email'] = emails[0]
            env.ref('base.user_admin').write(values)

            cr.commit()

    await loop.run_in_executor(None, configure_sync)


@check_db_management_enabled
async def async_exp_drop(db_name):
    """Drop a database asynchronously."""
    if db_name not in db_list(True):
        return False
    
    # TODO: Clean up registry and close connections
    # odoo.modules.registry.Registry.delete(db_name)
    # await async_close_db(db_name)

    connection = await async_db_connect('postgres')
    async with connection.cursor() as cr:
        # Database-altering operations cannot be executed inside a transaction
        await cr.execute("SET autocommit = true")
        
        # Drop connections to the database
        await cr.execute("""
            SELECT pg_terminate_backend(pid)
            FROM pg_stat_activity
            WHERE datname = %s AND pid <> pg_backend_pid()
        """, (db_name,))

        try:
            await cr.execute(f'DROP DATABASE "{db_name}"')
        except Exception as e:
            _logger.info('DROP DB: %s failed:\n%s', db_name, e)
            raise Exception("Couldn't drop database %s: %s" % (db_name, e))
        else:
            _logger.info('DROP DB: %s', db_name)

    # Remove filestore
    fs = odoo.tools.config.filestore(db_name)
    if os.path.exists(fs):
        shutil.rmtree(fs)
    return True


async def async_exp_db_exist(db_name):
    """Check if database exists asynchronously."""
    try:
        connection = await async_db_connect(db_name)
        async with connection.cursor():
            return True
    except Exception:
        return False


async def async_exp_list(document=False):
    """List databases asynchronously."""
    if not config['list_db']:
        raise odoo.exceptions.AccessDenied()
    
    try:
        connection = await async_db_connect('postgres')
        cr = await connection.cursor()
        async with cr:
            await cr.execute("""
                SELECT datname 
                FROM pg_database 
                WHERE datdba = (SELECT usesysid FROM pg_user WHERE usename = current_user)
                AND NOT datistemplate
                ORDER BY datname
            """)
            result = await cr.fetchall()
            databases = [row[0] for row in result]
            
        if document:
            # Return XML document format
            doc = ET.Element('databases')
            for db in databases:
                db_elem = ET.SubElement(doc, 'database')
                db_elem.text = db
            return ET.tostring(doc, encoding='unicode')
        
        return databases
        
    except Exception as e:
        _logger.error('Error listing databases: %s', e, exc_info=True)
        return []


async def async_exp_change_admin_password(new_password):
    """Change admin password asynchronously."""
    config['admin_passwd'] = new_password
    config.save()
    return True


async def async_exp_migrate_databases(databases):
    """Migrate databases asynchronously."""
    for db_name in databases:
        _logger.info('Migrating database %s', db_name)
        try:
            # TODO: Implement async database migration
            connection = await async_db_connect(db_name)
            async with connection.cursor() as cr:
                # Run migration scripts
                await cr.execute("SELECT 1")  # Placeholder
                await cr.commit()
            _logger.info('Database %s migrated successfully', db_name)
        except Exception as e:
            _logger.error('Failed to migrate database %s: %s', db_name, e, exc_info=True)


# Async database management API
class AsyncDatabaseManager:
    """Async database management interface."""
    
    @staticmethod
    async def create_database(name, demo=False, lang='en_US', **kwargs):
        """Create a database asynchronously."""
        return await async_exp_create_database(name, demo, lang, **kwargs)
    
    @staticmethod
    async def drop_database(name):
        """Drop a database asynchronously."""
        return await async_exp_drop(name)
    
    @staticmethod
    async def list_databases():
        """List databases asynchronously."""
        return await async_exp_list()
    
    @staticmethod
    async def database_exists(name):
        """Check if database exists asynchronously."""
        return await async_exp_db_exist(name)


#----------------------------------------------------------
# Async db service dispatch
#----------------------------------------------------------

async def async_dispatch(method, params):
    """Async version of database service dispatch."""
    g = globals()
    async_method_name = 'async_exp_' + method

    if method in ['db_exist', 'list', 'list_lang', 'server_version']:
        # These methods can be called without password check
        if async_method_name in g:
            return await g[async_method_name](*params)
        else:
            # Fall back to sync version
            exp_method_name = 'exp_' + method
            from . import db
            if hasattr(db, exp_method_name):
                import asyncio
                loop = asyncio.get_event_loop()
                sync_func = getattr(db, exp_method_name)
                return await loop.run_in_executor(None, sync_func, *params)
            else:
                raise KeyError("Method not found: %s" % method)
    elif async_method_name in g:
        # Methods that require password check
        passwd = params[0]
        params = params[1:]
        from .db import check_super
        check_super(passwd)
        return await g[async_method_name](*params)
    else:
        # Fall back to sync version with password check
        exp_method_name = 'exp_' + method
        from . import db
        if hasattr(db, exp_method_name):
            passwd = params[0]
            params = params[1:]
            from .db import check_super
            check_super(passwd)
            import asyncio
            loop = asyncio.get_event_loop()
            sync_func = getattr(db, exp_method_name)
            return await loop.run_in_executor(None, sync_func, *params)
        else:
            raise KeyError("Method not found: %s" % method)


# Async RPC dispatch function
async def async_dispatch_rpc(service_name, method, params):
    """
    Perform an async RPC call.

    :param str service_name: either "common", "db" or "object".
    :param str method: the method name of the given service to execute
    :param list params: the parameters for method call
    :return: the return value of the called method
    :rtype: Any
    """
    import threading
    import asyncio
    from functools import partial

    if service_name == 'db':
        # Use async database dispatch
        return await async_dispatch(method, params)
    else:
        # For other services, fall back to sync dispatch in thread pool
        from odoo.http.utils import dispatch_rpc
        loop = asyncio.get_event_loop()
        sync_func = partial(dispatch_rpc, service_name, method, params)
        return await loop.run_in_executor(None, sync_func)
    
    @staticmethod
    async def migrate_databases(databases):
        """Migrate databases asynchronously."""
        return await async_migrate_databases(databases)


# Global async database manager instance
async_db_manager = AsyncDatabaseManager()
